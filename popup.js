/**
 * Bedalator Popup Script
 * Handles the popup interface and communication with content script
 */

class BedalatorPopup {
  constructor() {
    this.currentData = null;
    this.updateInterval = null;
    this.init();
  }

  init() {
    this.setupEventListeners();
    // Automatically load data when popup opens
    this.loadWorkData(true); // Force fresh data on popup open
    this.startAutoRefresh();
    console.log('Bedalator Popup: Initialized with automatic data loading');
  }

  /**
   * Set up event listeners for UI interactions
   */
  setupEventListeners() {
    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.loadWorkData(true);
    });
  }

  /**
   * Load work data from content script or storage
   */
  async loadWorkData(forceRefresh = false, isAutoUpdate = false) {
    try {
      // Show different indicators for manual vs auto updates
      if (isAutoUpdate) {
        this.showAutoUpdateIndicator(true);
      } else {
        this.showLoading(true);
      }
      this.hideError();

      let workData = null;

      // Always try to get fresh data from content script first
      workData = await this.getDataFromContentScript();

      // If no fresh data and not forcing refresh, try stored data
      if (!workData && !forceRefresh) {
        workData = await this.getStoredData();
      }

      if (workData) {
        this.currentData = workData;
        this.updateUI(workData);
      } else {
        // Only show errors for manual refreshes, not auto-updates
        if (!isAutoUpdate) {
          // Check if we're on the right page
          const isOnTrackingPage = await this.checkIfOnTrackingPage();
          if (isOnTrackingPage) {
            this.showError('Zeiterfassungselemente gefunden, aber keine Daten verfügbar. Bitte versuchen Sie es erneut.');
          } else {
            this.showError('Keine Zeiterfassungsseite geöffnet. Bitte öffnen Sie die Zeiterfassungsseite in einem Tab.');
          }
        }
      }
    } catch (error) {
      console.error('Bedalator Popup: Error loading work data:', error);
      // Only show errors for manual refreshes, not auto-updates
      if (!isAutoUpdate) {
        this.showError('Fehler beim Laden der Arbeitsdaten. Bitte versuchen Sie es erneut.');
      }
    } finally {
      if (isAutoUpdate) {
        this.showAutoUpdateIndicator(false);
      } else {
        this.showLoading(false);
      }
    }
  }

  /**
   * Get data from content script or cross-tab
   */
  async getDataFromContentScript() {
    // First try the active tab
    const activeTabData = await this.getDataFromActiveTab();
    if (activeTabData) {
      return activeTabData;
    }

    // If no data from active tab, try cross-tab data
    return await this.getCrossTabData();
  }

  /**
   * Get data from the currently active tab
   */
  async getDataFromActiveTab() {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          // Set a timeout to avoid hanging
          const timeout = setTimeout(() => {
            console.log('Bedalator Popup: Active tab timeout');
            resolve(null);
          }, 2000); // Shorter timeout for active tab

          chrome.tabs.sendMessage(tabs[0].id, { type: 'GET_WORK_DATA' }, (response) => {
            clearTimeout(timeout);
            if (chrome.runtime.lastError) {
              console.log('Bedalator Popup: Active tab not available:', chrome.runtime.lastError.message);
              resolve(null);
            } else {
              resolve(response);
            }
          });
        } else {
          resolve(null);
        }
      });
    });
  }

  /**
   * Get data from any tracking tab via background script
   */
  async getCrossTabData() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_CROSS_TAB_DATA' }, (response) => {
        if (chrome.runtime.lastError) {
          console.log('Bedalator Popup: Cross-tab data not available:', chrome.runtime.lastError.message);
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * Check if any tab has tracking elements
   */
  async checkIfOnTrackingPage() {
    // First check active tab
    const activeTabCompatible = await new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, { type: 'CHECK_PAGE_COMPATIBILITY' }, (response) => {
            if (chrome.runtime.lastError) {
              resolve(false);
            } else {
              resolve(response || false);
            }
          });
        } else {
          resolve(false);
        }
      });
    });

    if (activeTabCompatible) {
      return true;
    }

    // Check if we have any stored data from other tabs
    const crossTabData = await this.getCrossTabData();
    return crossTabData !== null;
  }

  /**
   * Get current data from storage (temporary only)
   */
  async getStoredData() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['currentWorkData'], (result) => {
        if (chrome.runtime.lastError) {
          console.error('Bedalator Popup: Error getting stored data:', chrome.runtime.lastError);
          resolve(null);
        } else {
          resolve(result.currentWorkData || null);
        }
      });
    });
  }

  /**
   * Update the UI with work data
   */
  updateUI(data) {
    // Update status indicator
    this.updateStatusIndicator(data.workStatus);

    // Update time displays
    const totalTimeElement = document.getElementById('totalTime');
    if (totalTimeElement) {
      totalTimeElement.textContent = data.totalTime || '00:00';
    }

    // Show/hide current session
    const currentSessionContainer = document.getElementById('currentSessionContainer');
    const currentSessionTimeElement = document.getElementById('currentSessionTime');

    if (currentSessionContainer) {
      if (data.workStatus === 'working' && data.currentSessionTime) {
        currentSessionContainer.style.display = 'block';
        if (currentSessionTimeElement) {
          currentSessionTimeElement.textContent = data.currentSessionTime;
        }
      } else {
        currentSessionContainer.style.display = 'none';
      }
    }

    // Update sessions list
    this.updateSessionsList(data.timeData || []);

    // Update last update time
    this.updateLastUpdateTime(data.timestamp);
  }

  /**
   * Update status indicator
   */
  updateStatusIndicator(status) {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');

    if (statusDot && statusText) {
      switch (status) {
        case 'working':
          statusDot.className = 'status-dot working';
          statusText.textContent = 'Aktiv';
          break;
        case 'not-working':
          statusDot.className = 'status-dot not-working';
          statusText.textContent = 'Inaktiv';
          break;
        default:
          statusDot.className = 'status-dot unknown';
          statusText.textContent = 'Unbekannt';
      }
    } else {
      console.warn('Bedalator Popup: Status indicator elements not found');
    }
  }

  /**
   * Update sessions list
   */
  updateSessionsList(sessions) {
    const sessionsList = document.getElementById('sessionsList');
    const noSessions = document.getElementById('noSessions');

    if (!sessionsList) {
      console.warn('Bedalator Popup: Sessions list element not found');
      return;
    }

    if (!sessions || sessions.length === 0) {
      if (noSessions) {
        noSessions.style.display = 'block';
      }
      sessionsList.innerHTML = '<div class="no-sessions">Keine Sitzungen erkannt</div>';
      return;
    }

    if (noSessions) {
      noSessions.style.display = 'none';
    }
    
    const sessionsHTML = sessions.map((session, index) => {
      const isActive = session.start && !session.end;
      const endTime = session.end || (isActive ? 'Aktiv' : 'Unbekannt');
      const duration = this.calculateSessionDuration(session);
      
      return `
        <div class="session-item ${isActive ? 'active' : ''}">
          <div class="session-times">
            <span class="start-time">${session.start}</span>
            <span class="separator">→</span>
            <span class="end-time ${isActive ? 'active' : ''}">${endTime}</span>
          </div>
          <div class="session-duration">${duration}</div>
        </div>
      `;
    }).join('');

    sessionsList.innerHTML = sessionsHTML;
  }

  /**
   * Calculate session duration
   */
  calculateSessionDuration(session) {
    if (!session.start) return '00:00';

    const startMinutes = this.timeToMinutes(session.start);
    let endMinutes;

    if (session.end) {
      endMinutes = this.timeToMinutes(session.end);
    } else {
      // Active session - use current time
      const now = new Date();
      endMinutes = now.getHours() * 60 + now.getMinutes();
    }

    let duration = endMinutes - startMinutes;
    if (duration < 0) {
      // Handle sessions spanning midnight
      duration = (24 * 60 - startMinutes) + endMinutes;
    }

    return this.minutesToTime(duration);
  }

  /**
   * Convert time string to minutes
   */
  timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Convert minutes to time string
   */
  minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * Update last update time
   */
  updateLastUpdateTime(timestamp) {
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement) {
      if (timestamp) {
        const date = new Date(timestamp);
        lastUpdateElement.textContent = date.toLocaleTimeString('de-DE');
      } else {
        lastUpdateElement.textContent = 'Nie';
      }
    } else {
      console.warn('Bedalator Popup: Last update element not found');
    }
  }

  /**
   * Show/hide loading indicator
   */
  showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = show ? 'flex' : 'none';
    } else {
      console.warn('Bedalator Popup: Loading indicator element not found');
    }
  }

  /**
   * Show error message
   */
  showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    if (errorMessage && errorText) {
      errorText.textContent = message;
      errorMessage.style.display = 'block';
    } else {
      console.warn('Bedalator Popup: Error message elements not found');
      console.error('Error to display:', message);
    }
  }

  /**
   * Hide error message
   */
  hideError() {
    const errorMessage = document.getElementById('errorMessage');
    if (errorMessage) {
      errorMessage.style.display = 'none';
    } else {
      console.warn('Bedalator Popup: Error message element not found');
    }
  }

  /**
   * Show/hide auto-update indicator
   */
  showAutoUpdateIndicator(show) {
    const indicator = document.getElementById('autoUpdateIndicator');
    if (indicator) {
      indicator.style.display = show ? 'inline' : 'none';
    }
  }



  /**
   * Start auto-refresh for real-time updates
   */
  startAutoRefresh() {
    // Clear any existing interval
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // Start with a shorter interval for immediate responsiveness
    this.updateInterval = setInterval(() => {
      // Only auto-refresh if we have data and no errors are showing
      const errorMessage = document.getElementById('errorMessage');
      const isErrorVisible = errorMessage && errorMessage.style.display !== 'none';

      if (!isErrorVisible) {
        if (this.currentData) {
          if (this.currentData.workStatus === 'working') {
            // More frequent updates when actively working (every 30 seconds)
            this.loadWorkData(false, true); // true = isAutoUpdate
          } else {
            // Less frequent updates when not working (every 60 seconds)
            // But still check periodically
            this.loadWorkData(false, true); // true = isAutoUpdate
          }
        } else {
          // Try to load data if we don't have any (every 30 seconds)
          this.loadWorkData(false, true); // true = isAutoUpdate
        }
      }
    }, 30000); // 30 seconds for good balance between responsiveness and performance

    console.log('Bedalator Popup: Auto-refresh started (30-second intervals)');
  }

  /**
   * Stop auto-refresh
   */
  stopAutoRefresh() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const popup = new BedalatorPopup();
  
  // Cleanup when popup is closed
  window.addEventListener('beforeunload', () => {
    popup.stopAutoRefresh();
  });
});
