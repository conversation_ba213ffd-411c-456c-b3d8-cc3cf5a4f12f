/**
 * Bedalator Popup Script
 * Handles the popup interface and communication with content script
 */

class BedalatorPopup {
  constructor() {
    this.currentData = null;
    this.updateInterval = null;
    this.init();
  }

  init() {
    this.setupEventListeners();
    // Automatically load data when popup opens
    this.loadWorkData(true); // Force fresh data on popup open
    this.startAutoRefresh();
    console.log('Bedalator Popup: Initialized with automatic data loading');
  }

  /**
   * Set up event listeners for UI interactions
   */
  setupEventListeners() {
    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.loadWorkData(true);
    });
  }

  /**
   * Load work data from content script or storage
   */
  async loadWorkData(forceRefresh = false) {
    try {
      this.showLoading(true);
      this.hideError();

      let workData = null;

      // Always try to get fresh data from content script first
      workData = await this.getDataFromContentScript();

      // If no fresh data and not forcing refresh, try stored data
      if (!workData && !forceRefresh) {
        workData = await this.getStoredData();
      }

      if (workData) {
        this.currentData = workData;
        this.updateUI(workData);
      } else {
        // Check if we're on the right page
        const isOnTrackingPage = await this.checkIfOnTrackingPage();
        if (isOnTrackingPage) {
          this.showError('Zeiterfassungselemente gefunden, aber keine Daten verfügbar. Bitte versuchen Sie es erneut.');
        } else {
          this.showError('Keine Daten verfügbar. Bitte navigieren Sie zur Zeiterfassungsseite.');
        }
      }
    } catch (error) {
      console.error('Bedalator Popup: Error loading work data:', error);
      this.showError('Fehler beim Laden der Arbeitsdaten. Bitte versuchen Sie es erneut.');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Get data directly from content script
   */
  async getDataFromContentScript() {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          // Set a timeout to avoid hanging
          const timeout = setTimeout(() => {
            console.log('Bedalator Popup: Content script timeout');
            resolve(null);
          }, 3000);

          chrome.tabs.sendMessage(tabs[0].id, { type: 'GET_WORK_DATA' }, (response) => {
            clearTimeout(timeout);
            if (chrome.runtime.lastError) {
              console.log('Bedalator Popup: Content script not available:', chrome.runtime.lastError.message);
              resolve(null);
            } else {
              resolve(response);
            }
          });
        } else {
          resolve(null);
        }
      });
    });
  }

  /**
   * Check if current page has tracking elements
   */
  async checkIfOnTrackingPage() {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, { type: 'CHECK_PAGE_COMPATIBILITY' }, (response) => {
            if (chrome.runtime.lastError) {
              resolve(false);
            } else {
              resolve(response || false);
            }
          });
        } else {
          resolve(false);
        }
      });
    });
  }

  /**
   * Get current data from storage (temporary only)
   */
  async getStoredData() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['currentWorkData'], (result) => {
        if (chrome.runtime.lastError) {
          console.error('Bedalator Popup: Error getting stored data:', chrome.runtime.lastError);
          resolve(null);
        } else {
          resolve(result.currentWorkData || null);
        }
      });
    });
  }

  /**
   * Update the UI with work data
   */
  updateUI(data) {
    // Update status indicator
    this.updateStatusIndicator(data.workStatus);

    // Update time displays
    document.getElementById('totalTime').textContent = data.totalTime || '00:00';
    
    // Show/hide current session
    const currentSessionContainer = document.getElementById('currentSessionContainer');
    if (data.workStatus === 'working' && data.currentSessionTime) {
      currentSessionContainer.style.display = 'block';
      document.getElementById('currentSessionTime').textContent = data.currentSessionTime;
    } else {
      currentSessionContainer.style.display = 'none';
    }

    // Update sessions list
    this.updateSessionsList(data.timeData || []);

    // Update last update time
    this.updateLastUpdateTime(data.timestamp);
  }

  /**
   * Update status indicator
   */
  updateStatusIndicator(status) {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');

    switch (status) {
      case 'working':
        statusDot.className = 'status-dot working';
        statusText.textContent = 'Aktiv';
        break;
      case 'not-working':
        statusDot.className = 'status-dot not-working';
        statusText.textContent = 'Inaktiv';
        break;
      default:
        statusDot.className = 'status-dot unknown';
        statusText.textContent = 'Unbekannt';
    }
  }

  /**
   * Update sessions list
   */
  updateSessionsList(sessions) {
    const sessionsList = document.getElementById('sessionsList');
    const noSessions = document.getElementById('noSessions');

    if (!sessions || sessions.length === 0) {
      noSessions.style.display = 'block';
      sessionsList.innerHTML = '<div class="no-sessions">Keine Sitzungen erkannt</div>';
      return;
    }

    noSessions.style.display = 'none';
    
    const sessionsHTML = sessions.map((session, index) => {
      const isActive = session.start && !session.end;
      const endTime = session.end || (isActive ? 'Aktiv' : 'Unbekannt');
      const duration = this.calculateSessionDuration(session);
      
      return `
        <div class="session-item ${isActive ? 'active' : ''}">
          <div class="session-times">
            <span class="start-time">${session.start}</span>
            <span class="separator">→</span>
            <span class="end-time ${isActive ? 'active' : ''}">${endTime}</span>
          </div>
          <div class="session-duration">${duration}</div>
        </div>
      `;
    }).join('');

    sessionsList.innerHTML = sessionsHTML;
  }

  /**
   * Calculate session duration
   */
  calculateSessionDuration(session) {
    if (!session.start) return '00:00';

    const startMinutes = this.timeToMinutes(session.start);
    let endMinutes;

    if (session.end) {
      endMinutes = this.timeToMinutes(session.end);
    } else {
      // Active session - use current time
      const now = new Date();
      endMinutes = now.getHours() * 60 + now.getMinutes();
    }

    let duration = endMinutes - startMinutes;
    if (duration < 0) {
      // Handle sessions spanning midnight
      duration = (24 * 60 - startMinutes) + endMinutes;
    }

    return this.minutesToTime(duration);
  }

  /**
   * Convert time string to minutes
   */
  timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Convert minutes to time string
   */
  minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * Update last update time
   */
  updateLastUpdateTime(timestamp) {
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (timestamp) {
      const date = new Date(timestamp);
      lastUpdateElement.textContent = date.toLocaleTimeString('de-DE');
    } else {
      lastUpdateElement.textContent = 'Nie';
    }
  }

  /**
   * Show/hide loading indicator
   */
  showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.style.display = show ? 'flex' : 'none';
  }

  /**
   * Show error message
   */
  showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    errorText.textContent = message;
    errorMessage.style.display = 'block';
  }

  /**
   * Hide error message
   */
  hideError() {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.style.display = 'none';
  }



  /**
   * Start auto-refresh for real-time updates
   */
  startAutoRefresh() {
    // Refresh every 15 seconds when working, every 60 seconds when not working
    this.updateInterval = setInterval(() => {
      if (this.currentData) {
        if (this.currentData.workStatus === 'working') {
          // More frequent updates when actively working
          this.loadWorkData(false);
        } else {
          // Less frequent updates when not working
          this.loadWorkData(false);
        }
      } else {
        // Try to load data if we don't have any
        this.loadWorkData(false);
      }
    }, 15000); // 15 seconds for responsive updates
  }

  /**
   * Stop auto-refresh
   */
  stopAutoRefresh() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const popup = new BedalatorPopup();
  
  // Cleanup when popup is closed
  window.addEventListener('beforeunload', () => {
    popup.stopAutoRefresh();
  });
});
