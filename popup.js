/**
 * Bedalator Popup Script
 * Handles the popup interface and communication with content script
 */

class BedalatorPopup {
  constructor() {
    this.currentData = null;
    this.updateInterval = null;
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadWorkData();
    this.startAutoRefresh();
    console.log('Bedalator Popup: Initialized');
  }

  /**
   * Set up event listeners for UI interactions
   */
  setupEventListeners() {
    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.loadWorkData(true);
    });

    // History button
    document.getElementById('historyBtn').addEventListener('click', () => {
      this.showHistory();
    });

    // Clear data button
    document.getElementById('clearBtn').addEventListener('click', () => {
      this.clearData();
    });

    // Close history modal
    document.getElementById('closeHistoryBtn').addEventListener('click', () => {
      this.hideHistory();
    });

    // Close modal when clicking outside
    document.getElementById('historyModal').addEventListener('click', (e) => {
      if (e.target.id === 'historyModal') {
        this.hideHistory();
      }
    });
  }

  /**
   * Load work data from content script or storage
   */
  async loadWorkData(forceRefresh = false) {
    try {
      this.showLoading(true);
      this.hideError();

      let workData = null;

      if (forceRefresh) {
        // Try to get fresh data from content script
        workData = await this.getDataFromContentScript();
      }

      if (!workData) {
        // Fallback to stored data
        workData = await this.getStoredData();
      }

      if (workData) {
        this.currentData = workData;
        this.updateUI(workData);
      } else {
        this.showError('No work data available. Make sure you are on a work tracking page.');
      }
    } catch (error) {
      console.error('Bedalator Popup: Error loading work data:', error);
      this.showError('Failed to load work data. Please try refreshing.');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Get data directly from content script
   */
  async getDataFromContentScript() {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, { type: 'GET_WORK_DATA' }, (response) => {
            if (chrome.runtime.lastError) {
              console.log('Bedalator Popup: Content script not available');
              resolve(null);
            } else {
              resolve(response);
            }
          });
        } else {
          resolve(null);
        }
      });
    });
  }

  /**
   * Get stored data from background script
   */
  async getStoredData() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_STORED_DATA' }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('Bedalator Popup: Error getting stored data:', chrome.runtime.lastError);
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * Update the UI with work data
   */
  updateUI(data) {
    // Update status indicator
    this.updateStatusIndicator(data.workStatus);

    // Update time displays
    document.getElementById('totalTime').textContent = data.totalTime || '00:00';
    
    // Show/hide current session
    const currentSessionContainer = document.getElementById('currentSessionContainer');
    if (data.workStatus === 'working' && data.currentSessionTime) {
      currentSessionContainer.style.display = 'block';
      document.getElementById('currentSessionTime').textContent = data.currentSessionTime;
    } else {
      currentSessionContainer.style.display = 'none';
    }

    // Update sessions list
    this.updateSessionsList(data.timeData || []);

    // Update last update time
    this.updateLastUpdateTime(data.timestamp);
  }

  /**
   * Update status indicator
   */
  updateStatusIndicator(status) {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');

    switch (status) {
      case 'working':
        statusDot.className = 'status-dot working';
        statusText.textContent = 'Working';
        break;
      case 'not-working':
        statusDot.className = 'status-dot not-working';
        statusText.textContent = 'Not Working';
        break;
      default:
        statusDot.className = 'status-dot unknown';
        statusText.textContent = 'Unknown';
    }
  }

  /**
   * Update sessions list
   */
  updateSessionsList(sessions) {
    const sessionsList = document.getElementById('sessionsList');
    const noSessions = document.getElementById('noSessions');

    if (!sessions || sessions.length === 0) {
      noSessions.style.display = 'block';
      sessionsList.innerHTML = '<div class="no-sessions">No work sessions detected</div>';
      return;
    }

    noSessions.style.display = 'none';
    
    const sessionsHTML = sessions.map((session, index) => {
      const isActive = session.start && !session.end;
      const endTime = session.end || (isActive ? 'Active' : 'Unknown');
      const duration = this.calculateSessionDuration(session);
      
      return `
        <div class="session-item ${isActive ? 'active' : ''}">
          <div class="session-times">
            <span class="start-time">${session.start}</span>
            <span class="separator">→</span>
            <span class="end-time ${isActive ? 'active' : ''}">${endTime}</span>
          </div>
          <div class="session-duration">${duration}</div>
        </div>
      `;
    }).join('');

    sessionsList.innerHTML = sessionsHTML;
  }

  /**
   * Calculate session duration
   */
  calculateSessionDuration(session) {
    if (!session.start) return '00:00';

    const startMinutes = this.timeToMinutes(session.start);
    let endMinutes;

    if (session.end) {
      endMinutes = this.timeToMinutes(session.end);
    } else {
      // Active session - use current time
      const now = new Date();
      endMinutes = now.getHours() * 60 + now.getMinutes();
    }

    let duration = endMinutes - startMinutes;
    if (duration < 0) {
      // Handle sessions spanning midnight
      duration = (24 * 60 - startMinutes) + endMinutes;
    }

    return this.minutesToTime(duration);
  }

  /**
   * Convert time string to minutes
   */
  timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Convert minutes to time string
   */
  minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * Update last update time
   */
  updateLastUpdateTime(timestamp) {
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (timestamp) {
      const date = new Date(timestamp);
      lastUpdateElement.textContent = date.toLocaleTimeString();
    } else {
      lastUpdateElement.textContent = 'Never';
    }
  }

  /**
   * Show/hide loading indicator
   */
  showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.style.display = show ? 'flex' : 'none';
  }

  /**
   * Show error message
   */
  showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    errorText.textContent = message;
    errorMessage.style.display = 'block';
  }

  /**
   * Hide error message
   */
  hideError() {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.style.display = 'none';
  }

  /**
   * Show work history
   */
  async showHistory() {
    try {
      const historyModal = document.getElementById('historyModal');
      const historyList = document.getElementById('historyList');
      
      historyModal.style.display = 'flex';
      historyList.innerHTML = 'Loading...';

      // Get history data from background script
      const history = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_DAILY_SUMMARY' }, resolve);
      });

      if (history && history.length > 0) {
        const historyHTML = history.map(day => `
          <div class="history-item">
            <div class="history-date">
              <span class="day-name">${day.dayName}</span>
              <span class="date">${day.date}</span>
            </div>
            <div class="history-time">${day.totalTime}</div>
            <div class="history-sessions">${day.sessions.length} sessions</div>
          </div>
        `).join('');
        
        historyList.innerHTML = historyHTML;
      } else {
        historyList.innerHTML = '<div class="no-history">No history data available</div>';
      }
    } catch (error) {
      console.error('Bedalator Popup: Error showing history:', error);
      document.getElementById('historyList').innerHTML = '<div class="error">Failed to load history</div>';
    }
  }

  /**
   * Hide work history
   */
  hideHistory() {
    const historyModal = document.getElementById('historyModal');
    historyModal.style.display = 'none';
  }

  /**
   * Clear all data
   */
  async clearData() {
    if (confirm('Are you sure you want to clear all work data? This action cannot be undone.')) {
      try {
        await new Promise((resolve) => {
          chrome.runtime.sendMessage({ type: 'CLEAR_DATA' }, resolve);
        });
        
        // Reset UI
        this.currentData = null;
        document.getElementById('totalTime').textContent = '00:00';
        document.getElementById('currentSessionContainer').style.display = 'none';
        document.getElementById('sessionsList').innerHTML = '<div class="no-sessions">No work sessions detected</div>';
        this.updateStatusIndicator('unknown');
        this.updateLastUpdateTime(null);
        
        alert('All data has been cleared.');
      } catch (error) {
        console.error('Bedalator Popup: Error clearing data:', error);
        alert('Failed to clear data. Please try again.');
      }
    }
  }

  /**
   * Start auto-refresh for real-time updates
   */
  startAutoRefresh() {
    // Refresh every 30 seconds when popup is open
    this.updateInterval = setInterval(() => {
      if (this.currentData && this.currentData.workStatus === 'working') {
        this.loadWorkData();
      }
    }, 30000);
  }

  /**
   * Stop auto-refresh
   */
  stopAutoRefresh() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const popup = new BedalatorPopup();
  
  // Cleanup when popup is closed
  window.addEventListener('beforeunload', () => {
    popup.stopAutoRefresh();
  });
});
