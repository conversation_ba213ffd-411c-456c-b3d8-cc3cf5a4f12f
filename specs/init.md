You are an AI senior software engineer. and your job is to help me create a production ready chrome extension.
Chrome Extension Development Plan: Daily Work Time Calculator. the chrome extension name should be Bedalator.

## Overview
This plan outlines the development of a Chrome extension that calculates daily work hours by parsing HTML time data from a work tracking system. The extension provides real-time calculation without storing historical data, features a sleek black-on-black modern UI design, and uses German language throughout the interface.

## Core Features

### Time Tracking Logic
- Parse completed work sessions from the HTML table (start-end time pairs)
- Detect active work sessions when the second column is empty
- Real-time calculation of current session duration without data storage
- Daily total combining completed and ongoing sessions
- **Automatic Data Loading**: Extension automatically loads and displays data when popup opens
- **Enhanced Active Session Calculation**: Finds most recent incomplete session for accurate ongoing time
- **Cross-Tab Functionality**: Continues tracking even when time tracking tab is not active
- **Background Time Calculation**: Persistent updates regardless of tab visibility or browser focus
- **Intelligent Refresh**: 30-second background updates with visibility-aware frequency adjustment

### Status Detection
- "Kommen" button = Not currently working (Inaktiv)
- "<PERSON>eh<PERSON>" button = Currently working (Aktiv)
- Use button text to determine work status
- Visual status indicators with animated effects

### Language & Localization
- Complete German language interface
- German time format (de-DE locale)
- Localized status messages and error handling
- German labels for all UI components

## Technical Architecture

### Content Script (content.js)
```javascript
// Key functions implemented:
- extractTimeData() // Extract time pairs from HTML table
- calculateDailyTotal() // Calculate total work time in real-time
- getCurrentSessionDuration() // Calculate ongoing session if active
- getCurrentWorkStatus() // Check button text for current status
- collectWorkData() // Gather all work information without storage
```

### Background Script (background.js)
```javascript
// Handles:
- Extension lifecycle management
- Real-time badge updates
- Temporary data storage for popup communication
- German language support in notifications
```

### Popup Interface (popup.html)
```html
<!-- German UI displaying:
- Gesamte Tageszeit (Total daily time)
- Aktuelle Sitzung (Current session duration if working)
- Aktuelle Sitzungen (List of current sessions)
- Status indicator (Aktiv/Inaktiv/Unbekannt)
- Single scrollable container for improved UX
-->
```

### UI/UX Design Specifications
- **Color Scheme**: Black-on-black modern design (#0a0a0a background)
- **Typography**: SF Mono for time displays, Inter for UI text
- **Animations**: Pulsing green dot for active status
- **Scrolling**: Single consolidated scrollable container with optimized height
- **Responsive**: 360px width, 580px height (increased for better content visibility)
- **Layout Optimization**: Reduced padding and margins to maximize session list space
- **Session Display**: Compact session items with improved spacing for more visible entries
- **Accessibility**: High contrast, clear visual hierarchy
## Implementation Steps

### Phase 1: HTML Parsing & Real-time Processing
- Identify data table using selector: `#DataGridVorhandeneBuchungen table tbody`
- Extract time pairs from each row's two cells
- Parse time format (HH:MM) into minutes for calculations
- Handle empty second columns (ongoing sessions)
- Real-time calculation without persistent storage

### Phase 2: Time Calculations
Calculate completed sessions:
```javascript
completedMinutes = endTime - startTime (for each complete pair)
```

Calculate current session (if second column empty):
```javascript
currentMinutes = currentTime - lastStartTime
```

Format display time as HH:MM with German locale

### Phase 3: Status Detection & German UI
- Monitor button text in `#divTerminalbuttons`
- Check for "Gehen" = currently working (Aktiv)
- Check for "Kommen" = not working (Inaktiv)
- Update calculations and UI based on status
- Display German status messages

### Phase 4: Extension Integration & UI Optimization
- **Robust Content Script Injection**: Multiple initialization attempts with element detection
- **Enhanced Message Passing**: Timeout protection and error handling for popup-content communication
- **Automatic Data Loading**: Popup immediately loads fresh data on open without manual refresh
- **Intelligent Updates**: 15-second refresh cycles with status-aware frequency
- **Optimized Layout**: 580px height with reduced padding for maximum session visibility
- **Error Handling**: German error messages with page compatibility checking
- **Single Scrollable Container**: Consolidated sessions display with improved scrolling UX

File Structure
text
work-time-calculator/
├── manifest.json
├── content.js
├── background.js
├── popup.html
├── popup.js
├── popup.css
└── icons/
├── icon16.png
├── icon48.png
└── icon128.png
Key Functions to Implement
Data Extraction
javascript
function extractTimeData() {
const rows = document.querySelectorAll('#DataGridVorhandeneBuchungen tbody tr:not(.dx-freespace-row)');
return Array.from(rows).map(row => {
const cells = row.querySelectorAll('td');
return {
start: cells[0]?.textContent.trim(),
end: cells[1]?.textContent.trim() || null
};
});
}
Work Status Detection
javascript
function getCurrentWorkStatus() {
const button = document.querySelector('#divTerminalbuttons .dx-button-text');
return button?.textContent === 'Gehen' ? 'working' : 'not-working';
}
Time Calculation
javascript
function calculateDailyTotal(timeData, currentStatus) {
let totalMinutes = 0;

// Add completed sessions
timeData.forEach(session => {
if (session.start && session.end) {
totalMinutes += timeToMinutes(session.end) - timeToMinutes(session.start);
}
});

// Add current session if working
if (currentStatus === 'working') {
const lastIncomplete = timeData.find(s => s.start && !s.end);
if (lastIncomplete) {
const now = new Date();
const currentMinutes = now.getHours() * 60 + now.getMinutes();
totalMinutes += currentMinutes - timeToMinutes(lastIncomplete.start);
}
}

return totalMinutes;
}
## User Interface Design

### Popup Display (German Interface)
- Large timer display showing "Gesamte Tageszeit" (total daily hours)
- Current session timer "Aktuelle Sitzung" (if actively working)
- Session list "Aktuelle Sitzungen" with start/end times
- Status indicator (Aktiv/Inaktiv/Unbekannt)
- "Aktualisieren" button to refresh data
- Single consolidated scrollable container

### Visual Elements & Design System
- **Active Status**: Pulsing green indicator (#00ff88) with animation
- **Inactive Status**: Red indicator (#ff4757)
- **Unknown Status**: Gray indicator (#666666)
- **Background**: Deep black (#0a0a0a) with subtle gradients
- **Typography**: SF Mono for time displays, clean sans-serif for UI
- **Layout**: 360px × 520px responsive design
- **Scrolling**: Optimized single-container scrolling experience
- **Accessibility**: High contrast ratios, clear visual hierarchy

### German Language Elements
- All labels and messages in German
- German time format (de-DE locale)
- Localized error messages and status indicators
- German button text and tooltips

## Data Management Strategy

### Real-time Calculation Approach
- **No Historical Storage**: Extension calculates time in real-time only
- **Temporary Session Data**: Only current session data stored temporarily
- **Page-dependent**: Calculations only occur when on time tracking page
- **Memory Efficient**: Minimal data footprint, no long-term storage
- **Privacy Focused**: No persistent user data collection

### Performance Optimization
- **Efficient DOM Parsing**: Optimized selectors for target elements
- **Minimal Resource Usage**: Lightweight background processing
- **Smart Updates**: Only recalculate when data changes detected
- **Responsive UI**: Smooth scrolling and animations

## Bug Fixes & Improvements (Latest Version)

### Refresh Button Reliability
- **Issue Fixed**: Refresh button error on subsequent clicks
- **Solution**: Enhanced error handling with timeout protection and page compatibility checking
- **Improvement**: Automatic fresh data loading on popup open eliminates need for manual refresh

### Active Session Calculation Enhancement
- **Issue Fixed**: Inaccurate active session time calculation
- **Solution**: Improved algorithm to find most recent incomplete session
- **Enhancement**: Real-time updates every 15 seconds during active work periods

### Automatic Data Loading
- **Feature Added**: Popup automatically loads current work data on open
- **Benefit**: Eliminates manual refresh requirement for immediate data visibility
- **Implementation**: Force fresh data retrieval on popup initialization

### UI Layout Optimization
- **Height Increased**: From 520px to 580px for better content visibility
- **Padding Reduced**: Optimized spacing throughout interface
- **Session Items**: Compact design allows more entries to be visible
- **Scrolling Improved**: Single container with better scroll behavior

### Error Handling Enhancement
- **German Error Messages**: Localized error feedback
- **Page Compatibility**: Checks for required elements before showing errors
- **Timeout Protection**: Prevents hanging on content script communication
- **Graceful Degradation**: Fallback to stored data when fresh data unavailable

Testing Strategy
Test Cases
Parse completed sessions correctly
Handle ongoing sessions with empty end times
Detect work status from button text
Calculate time accurately across different scenarios
Handle page reloads and data persistence
Real-time updates during active work sessions

Edge Cases
Multiple incomplete sessions
Sessions spanning midnight
Invalid time formats
Missing HTML elements
Network connectivity issues