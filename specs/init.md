You are an AI senior software engineer. and your job is to help me create a production ready chrome extension.
Chrome Extension Development Plan: Daily Work Time Calculator. the chrome extension name should be Bedalator.

## Overview
This plan outlines the development of a Chrome extension that calculates daily work hours by parsing HTML time data from a work tracking system. The extension provides real-time calculation without storing historical data, features a sleek black-on-black modern UI design, and uses German language throughout the interface.

Core Features
Time Tracking Logic
Parse completed work sessions from the HTML table (start-end time pairs)

Detect active work sessions when the second column is empty

Real-time calculation of current session duration

Daily total combining completed and ongoing sessions

Status Detection
"Kommen" button = Not currently working

"Gehen" button = Currently working

Use button text to determine work status

Technical Architecture
Content Script (content.js)
javascript
// Key functions to implement:
- parseTimeData() // Extract time pairs from HTML table
- calculateCompletedTime() // Sum all completed sessions
- getCurrentSessionTime() // Calculate ongoing session if active
- getTotalDailyTime() // Combine completed + current session
- detectWorkStatus() // Check button text for current status
  Background Script (background.js)
  javascript
  // Handle:
- Extension lifecycle management
- Data persistence across page reloads
- Communication with popup
  Popup Interface (popup.html)
  xml
<!-- Display:
- Today's total work time
- Current session duration (if working)
- List of completed sessions
- Work status indicator
-->
Implementation Steps
Phase 1: HTML Parsing
Identify data table using selector: #DataGridVorhandeneBuchungen table tbody

Extract time pairs from each row's two cells

Parse time format (HH:MM) into minutes for calculations

Handle empty second columns (ongoing sessions)

Phase 2: Time Calculations
Calculate completed sessions:

javascript
completedMinutes = endTime - startTime (for each complete pair)
Calculate current session (if second column empty):

javascript
currentMinutes = currentTime - lastStartTime
Format display time as HH:MM

Phase 3: Status Detection
Monitor button text in #divTerminalbuttons

Check for "Gehen" = currently working

Check for "Kommen" = not working

Update calculations based on status

Phase 4: Extension Integration
Content script injection to monitor the page

Message passing between content script and popup

Real-time updates every minute when actively working

Data persistence using Chrome storage API

File Structure
text
work-time-calculator/
├── manifest.json
├── content.js
├── background.js
├── popup.html
├── popup.js
├── popup.css
└── icons/
├── icon16.png
├── icon48.png
└── icon128.png
Key Functions to Implement
Data Extraction
javascript
function extractTimeData() {
const rows = document.querySelectorAll('#DataGridVorhandeneBuchungen tbody tr:not(.dx-freespace-row)');
return Array.from(rows).map(row => {
const cells = row.querySelectorAll('td');
return {
start: cells[0]?.textContent.trim(),
end: cells[1]?.textContent.trim() || null
};
});
}
Work Status Detection
javascript
function getCurrentWorkStatus() {
const button = document.querySelector('#divTerminalbuttons .dx-button-text');
return button?.textContent === 'Gehen' ? 'working' : 'not-working';
}
Time Calculation
javascript
function calculateDailyTotal(timeData, currentStatus) {
let totalMinutes = 0;

// Add completed sessions
timeData.forEach(session => {
if (session.start && session.end) {
totalMinutes += timeToMinutes(session.end) - timeToMinutes(session.start);
}
});

// Add current session if working
if (currentStatus === 'working') {
const lastIncomplete = timeData.find(s => s.start && !s.end);
if (lastIncomplete) {
const now = new Date();
const currentMinutes = now.getHours() * 60 + now.getMinutes();
totalMinutes += currentMinutes - timeToMinutes(lastIncomplete.start);
}
}

return totalMinutes;
}
User Interface Design
Popup Display
Large timer display showing total daily hours
Current session timer (if actively working)
Session list with start/end times
Status indicator (Working/Not Working)
Refresh button to update data

Visual Elements
Green indicator when working
Red indicator when not working
Clean, professional design matching workplace tools
Responsive layout for different screen sizes

Testing Strategy
Test Cases
Parse completed sessions correctly
Handle ongoing sessions with empty end times
Detect work status from button text
Calculate time accurately across different scenarios
Handle page reloads and data persistence
Real-time updates during active work sessions

Edge Cases
Multiple incomplete sessions
Sessions spanning midnight
Invalid time formats
Missing HTML elements
Network connectivity issues