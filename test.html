<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bedalator Test Page</title>
</head>
<body>
    <h1>Test Page for Bedalator Extension</h1>
    
    <!-- Your actual HTML structure -->
    <div id="divTerminalbuttonsBuchungen" class="ct-InhaltContainer">
        <div id="divTerminalbuttons">
            <div class="ct-TerminalButtonZeile">
                <div class="dx-widget dx-button dx-button-mode-contained dx-button-normal dx-button-has-text dx-button-has-icon ct-TerminalButton ct-TerminalButtonSpalte" role="button" aria-label="Kommen" tabindex="0" style="height: 55px; width: 100%;">
                    <div class="dx-button-content">
                        <i class="dx-icon dx-svg-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" style="fill-rule:evenodd;" viewBox="0 0 10000 10000" fill="#00CC00" stroke="#00CC00" stroke-width="0" height="100%" width="auto">
                                <path d="M4200 3963l1185 1037 -1185 1037 0 -637 -3200 0 0 -800 3200 0 0 -637zm-800 -1763l0 1600 -3200 0 0 2400 3200 0 0 1600 3200 -2800 -3200 -2800zm2400 -1200c-715,0 -1384,189 -1964,518l91 80 558 488c402,-182 846,-286 1315,-286 1765,0 3200,1436 3200,3200 0,1764 -1435,3200 -3200,3200 -469,0 -913,-104 -1315,-286l-559 489 -91 80c581,328 1250,517 1965,517 2209,0 4000,-1791 4000,-4000 0,-2209 -1791,-4000 -4000,-4000l0 0z"></path>
                            </svg>
                        </i>
                        <span class="dx-button-text">Kommen</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="divBuchungen">
            <div class="dx-form dx-widget dx-visibility-change-handler" role="form">
                <div class="dx-layout-manager dx-widget">
                    <div class="dx-widget dx-collection dx-responsivebox-screen-lg dx-responsivebox" style="width: 100%; height: 100%;">
                        <div class="dx-box-flex dx-box dx-widget dx-collection" style="display: flex; flex-direction: row; width: 100%; height: 100%; justify-content: flex-start; align-items: stretch;">
                            <div class="dx-item dx-box-item" style="display: flex; max-width: none; min-width: 0px; flex: 1 1 auto;">
                                <div class="dx-item-content dx-box-item-content" style="width: auto; height: auto; display: flex; flex-basis: 0px; flex-grow: 1; flex-direction: column;">
                                    <div class="dx-first-row dx-first-col dx-last-col dx-last-row dx-field-item dx-col-0 dx-field-item-optional dx-field-item-has-group">
                                        <div class="dx-field-item-content dx-field-item-content-location-right">
                                            <div class="dx-form-group-with-caption dx-form-group">
                                                <span class="dx-form-group-caption">vorhandene Buchungen</span>
                                                <div class="dx-form-group-content">
                                                    <div class="dx-widget dx-visibility-change-handler" id="DataGridVorhandeneBuchungen" role="presentation" style="width: 10em; height: 30vh;">
                                                        <div class="dx-datagrid dx-gridbase-container" role="grid" aria-label="Datentabelle" aria-rowcount="4" aria-colcount="2">
                                                            <div class="dx-datagrid-rowsview dx-datagrid-nowrap dx-scrollable dx-visibility-change-handler dx-scrollable-both dx-scrollable-native dx-scrollable-native-generic" role="presentation">
                                                                <div class="dx-scrollable-wrapper">
                                                                    <div class="dx-scrollable-container">
                                                                        <div class="dx-scrollable-content">
                                                                            <div class="dx-datagrid-content">
                                                                                <table class="dx-datagrid-table dx-datagrid-table-fixed" role="presentation">
                                                                                    <colgroup>
                                                                                        <col style="width: 5em;">
                                                                                        <col style="width: auto;">
                                                                                    </colgroup>
                                                                                    <tbody role="presentation">
                                                                                        <tr class="dx-row dx-data-row" role="row" aria-rowindex="1">
                                                                                            <td role="gridcell" aria-colindex="1" tabindex="0" style="text-align: left;">17:00 </td>
                                                                                            <td role="gridcell" aria-colindex="2" style="text-align: left;">18:24 </td>
                                                                                        </tr>
                                                                                        <tr class="dx-row dx-data-row" role="row" aria-rowindex="2">
                                                                                            <td role="gridcell" aria-colindex="1" style="text-align: left;">19:18 </td>
                                                                                            <td role="gridcell" aria-colindex="2" style="text-align: left;">19:38 </td>
                                                                                        </tr>
                                                                                        <tr class="dx-row dx-data-row" role="row" aria-rowindex="3">
                                                                                            <td role="gridcell" aria-colindex="1" style="text-align: left;">19:54 </td>
                                                                                            <td role="gridcell" aria-colindex="2" style="text-align: left;">20:53 </td>
                                                                                        </tr>
                                                                                        <tr class="dx-row dx-data-row" role="row" aria-rowindex="4">
                                                                                            <td role="gridcell" aria-colindex="1" style="text-align: left;">22:13 </td>
                                                                                            <td role="gridcell" aria-colindex="2" style="text-align: left;">22:14 </td>
                                                                                        </tr>
                                                                                        <tr class="dx-row dx-freespace-row" role="presentation" style="height: 153.391px;">
                                                                                            <td style="text-align: left;"></td>
                                                                                            <td style="text-align: left;"></td>
                                                                                        </tr>
                                                                                    </tbody>
                                                                                </table>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test the extension's parsing logic
        console.log('Testing Bedalator parsing...');
        
        // Test time data extraction
        const rows = document.querySelectorAll('#DataGridVorhandeneBuchungen tbody tr:not(.dx-freespace-row)');
        console.log('Found rows:', rows.length);
        
        rows.forEach((row, index) => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 2) {
                const startTime = cells[0]?.textContent.trim();
                const endTime = cells[1]?.textContent.trim() || null;
                console.log(`Session ${index + 1}: ${startTime} → ${endTime}`);
            }
        });
        
        // Test status detection
        const buttons = document.querySelectorAll('#divTerminalbuttons .dx-button-text');
        buttons.forEach(button => {
            console.log('Button text:', button.textContent.trim());
        });
    </script>
</body>
</html>
