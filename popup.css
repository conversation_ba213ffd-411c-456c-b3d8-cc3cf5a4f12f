/**
 * Bedalator Popup Styles
 * Sleek black-on-black modern design
 */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Inter', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #ffffff;
  background: #0a0a0a;
  width: 360px;
  height: 520px;
  font-weight: 400;
  overflow: hidden; /* Prevent body from scrolling */
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 520px;
  background: #0a0a0a;
  overflow: hidden; /* Prevent container from scrolling */
}

/* Header */
.header {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  color: #ffffff;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333333;
}

.logo h1 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 2px;
  letter-spacing: -0.5px;
}

.logo .subtitle {
  font-size: 11px;
  opacity: 0.7;
  color: #888888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  padding: 8px 14px;
  border-radius: 12px;
  font-size: 11px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.working {
  background: #00ff88;
  box-shadow: 0 0 12px rgba(0, 255, 136, 0.4);
  animation: pulse 2s infinite;
}

.status-dot.not-working {
  background: #ff4757;
  box-shadow: 0 0 8px rgba(255, 71, 87, 0.3);
}

.status-dot.unknown {
  background: #666666;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Time Display */
.time-display {
  padding: 32px 24px;
  background: #111111;
  border-bottom: 1px solid #333333;
}

.total-time, .current-session {
  text-align: center;
  margin-bottom: 20px;
}

.total-time label, .current-session label {
  display: block;
  font-size: 11px;
  color: #888888;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.time-value {
  font-size: 42px;
  font-weight: 200;
  color: #ffffff;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: -1px;
}

.time-value.current {
  font-size: 28px;
  color: #00ff88;
  font-weight: 300;
}

/* Sessions Section */
.sessions-section {
  flex: 1;
  padding: 24px 24px 0 24px;
  background: #0a0a0a;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex child to shrink */
}

.sessions-section h3 {
  font-size: 14px;
  margin-bottom: 20px;
  color: #ffffff;
  border-bottom: 1px solid #333333;
  padding-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  flex-shrink: 0; /* Prevent header from shrinking */
}

.sessions-container {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 24px;
  min-height: 0; /* Important for flex child to shrink */
}

.sessions-list {
  /* Remove max-height and overflow to let parent handle scrolling */
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 12px;
  background: #1a1a1a;
  border-radius: 12px;
  border-left: 3px solid #333333;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.session-item:hover {
  background: #222222;
  border-left-color: #555555;
  transform: translateY(-1px);
}

.session-item.active {
  border-left-color: #00ff88;
  background: rgba(0, 255, 136, 0.05);
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.1);
}

.session-times {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
}

.separator {
  color: #666666;
  font-weight: 300;
}

.end-time.active {
  color: #00ff88;
  font-weight: 500;
}

.session-duration {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 500;
  color: #cccccc;
  font-size: 13px;
}

.no-sessions {
  text-align: center;
  color: #666666;
  font-style: italic;
  padding: 40px 20px;
  font-size: 13px;
}

/* Controls */
.controls {
  padding: 20px 24px;
  background: #111111;
  border-top: 1px solid #333333;
  display: flex;
  gap: 12px;
}

.btn {
  flex: 1;
  padding: 14px 20px;
  border: none;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-primary {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  color: #ffffff;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
  border-color: rgba(255, 255, 255, 0.2);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-icon {
  font-size: 16px;
  opacity: 0.8;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 16px 20px;
  background: #667eea;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

/* History */
.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  display: flex;
  flex-direction: column;
}

.day-name {
  font-weight: 500;
  color: #333;
}

.date {
  font-size: 12px;
  color: #666;
}

.history-time {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  font-size: 16px;
  color: #667eea;
}

.history-sessions {
  font-size: 12px;
  color: #666;
}

.no-history {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 20px;
}

/* Error Message */
.error-message {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  padding: 16px 20px;
  margin: 16px 24px;
  border-radius: 10px;
  border: 1px solid rgba(255, 71, 87, 0.2);
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
}

.error-icon {
  font-size: 18px;
}

/* Loading Indicator */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #888888;
}

.spinner {
  width: 28px;
  height: 28px;
  border: 2px solid #333333;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer */
.footer {
  padding: 16px 24px;
  background: #111111;
  border-top: 1px solid #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #666666;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Specific scrollbar styling for sessions container */
.sessions-container::-webkit-scrollbar {
  width: 4px;
}

.sessions-container::-webkit-scrollbar-thumb {
  background: #444444;
  border-radius: 2px;
}

.sessions-container::-webkit-scrollbar-thumb:hover {
  background: #666666;
}

/* Responsive adjustments */
@media (max-height: 600px) {
  .sessions-list {
    max-height: 150px;
  }
  
  .time-value {
    font-size: 28px;
  }
}
