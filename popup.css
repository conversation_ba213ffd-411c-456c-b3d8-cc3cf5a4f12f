/**
 * Bedalator Popup Styles
 * Clean, professional design for the work time calculator popup
 */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background: #f8f9fa;
  width: 380px;
  min-height: 500px;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 2px;
}

.logo .subtitle {
  font-size: 12px;
  opacity: 0.9;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.working {
  background: #4CAF50;
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-dot.not-working {
  background: #F44336;
}

.status-dot.unknown {
  background: #9E9E9E;
}

/* Time Display */
.time-display {
  padding: 24px 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.total-time, .current-session {
  text-align: center;
  margin-bottom: 16px;
}

.total-time label, .current-session label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.time-value {
  font-size: 36px;
  font-weight: 300;
  color: #333;
  font-family: 'Courier New', monospace;
}

.time-value.current {
  font-size: 24px;
  color: #4CAF50;
}

/* Sessions Section */
.sessions-section {
  flex: 1;
  padding: 20px;
  background: white;
  overflow-y: auto;
}

.sessions-section h3 {
  font-size: 16px;
  margin-bottom: 16px;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.sessions-list {
  max-height: 200px;
  overflow-y: auto;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #e0e0e0;
  transition: all 0.2s ease;
}

.session-item:hover {
  background: #f0f0f0;
}

.session-item.active {
  border-left-color: #4CAF50;
  background: #f1f8e9;
}

.session-times {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Courier New', monospace;
}

.separator {
  color: #999;
}

.end-time.active {
  color: #4CAF50;
  font-weight: 500;
}

.session-duration {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #666;
}

.no-sessions {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 20px;
}

/* Controls */
.controls {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 8px;
}

.btn {
  flex: 1;
  padding: 10px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-icon {
  font-size: 14px;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 16px 20px;
  background: #667eea;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

/* History */
.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  display: flex;
  flex-direction: column;
}

.day-name {
  font-weight: 500;
  color: #333;
}

.date {
  font-size: 12px;
  color: #666;
}

.history-time {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  font-size: 16px;
  color: #667eea;
}

.history-sessions {
  font-size: 12px;
  color: #666;
}

.no-history {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 20px;
}

/* Error Message */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  margin: 16px 20px;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  font-size: 16px;
}

/* Loading Indicator */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer */
.footer {
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #666;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-height: 600px) {
  .sessions-list {
    max-height: 150px;
  }
  
  .time-value {
    font-size: 28px;
  }
}
