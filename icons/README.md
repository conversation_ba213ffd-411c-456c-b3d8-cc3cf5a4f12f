# Bedalator Icons

This directory contains the icon files for the Bedalator Chrome extension.

## Required Icons

The following icon files are required for the Chrome extension:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Icon Design Guidelines

The icons should:
- Use a consistent design theme
- Be clearly visible at small sizes
- Represent time/work tracking functionality
- Follow Chrome extension icon guidelines
- Use appropriate colors that work on both light and dark backgrounds

## Current Status

**PLACEHOLDER ICONS NEEDED**: The current implementation uses placeholder references. 
You need to create actual PNG icon files with the specified dimensions.

## Suggested Icon Design

The icon could feature:
- A clock or timer symbol
- Work-related imagery (briefcase, calendar, etc.)
- The "B" letter for Bedalator branding
- Colors that match the extension's purple/blue theme (#667eea)

## Creating Icons

You can create icons using:
- Design tools like Figma, Sketch, or Adobe Illustrator
- Online icon generators
- Icon libraries with appropriate licensing
- AI image generators for custom designs

Make sure to export in PNG format with transparent backgrounds where appropriate.
