/**
 * Bedalator Content Script
 * Parses HTML time data from work tracking system and calculates daily work hours
 */

class BedalatorTimeTracker {
  constructor() {
    this.updateInterval = null;
    this.lastKnownData = null;
    this.visibilityUpdateInterval = null;
    this.init();
  }

  init() {
    // Start monitoring if we're on a relevant page
    if (this.isWorkTrackingPage()) {
      this.registerWithBackground();
      this.startMonitoring();
      this.setupVisibilityHandling();
      console.log('Bedalator: Time tracking initialized with background support');
    }
  }

  /**
   * Register this tab with the background script
   */
  registerWithBackground() {
    chrome.runtime.sendMessage({ type: 'REGISTER_TRACKING_TAB' }).catch(error => {
      console.log('Bedalator: Could not register with background script');
    });
  }

  isWorkTrackingPage() {
    // Check if the page contains the expected elements
    return document.querySelector('#DataGridVorhandeneBuchungen') !== null ||
           document.querySelector('#divTerminalbuttons') !== null;
  }

  /**
   * Extract time data from the HTML table
   * Targets: #DataGridVorhandeneBuchungen table tbody
   */
  extractTimeData() {
    try {
      const rows = document.querySelectorAll('#DataGridVorhandeneBuchungen tbody tr:not(.dx-freespace-row)');
      const timeData = [];

      rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const startTime = cells[0]?.textContent.trim();
          const endTime = cells[1]?.textContent.trim() || null;
          
          if (startTime && this.isValidTimeFormat(startTime)) {
            timeData.push({
              start: startTime,
              end: endTime && this.isValidTimeFormat(endTime) ? endTime : null
            });
          }
        }
      });

      return timeData;
    } catch (error) {
      console.error('Bedalator: Error extracting time data:', error);
      return [];
    }
  }

  /**
   * Detect current work status from button text
   * Targets: #divTerminalbuttons .dx-button-text
   */
  getCurrentWorkStatus() {
    try {
      const buttons = document.querySelectorAll('#divTerminalbuttons .dx-button-text');

      for (const button of buttons) {
        const buttonText = button?.textContent?.trim();

        if (buttonText === 'Gehen') {
          return 'working';
        } else if (buttonText === 'Kommen') {
          return 'not-working';
        }
      }

      return 'unknown';
    } catch (error) {
      console.error('Bedalator: Error detecting work status:', error);
      return 'unknown';
    }
  }

  /**
   * Convert time string (HH:MM) to minutes
   */
  timeToMinutes(timeString) {
    if (!timeString || !this.isValidTimeFormat(timeString)) {
      return 0;
    }

    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Convert minutes to time string (HH:MM)
   */
  minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * Validate time format (HH:MM)
   */
  isValidTimeFormat(timeString) {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(timeString);
  }

  /**
   * Calculate total daily work time
   */
  calculateDailyTotal(timeData, currentStatus) {
    let totalMinutes = 0;

    // Add completed sessions
    timeData.forEach(session => {
      if (session.start && session.end) {
        const startMinutes = this.timeToMinutes(session.start);
        const endMinutes = this.timeToMinutes(session.end);
        
        // Handle sessions that might span midnight
        if (endMinutes >= startMinutes) {
          totalMinutes += endMinutes - startMinutes;
        } else {
          // Session spans midnight
          totalMinutes += (24 * 60 - startMinutes) + endMinutes;
        }
      }
    });

    // Add current session if working
    if (currentStatus === 'working') {
      const lastIncomplete = timeData.find(session => session.start && !session.end);
      if (lastIncomplete) {
        const now = new Date();
        const currentMinutes = now.getHours() * 60 + now.getMinutes();
        const startMinutes = this.timeToMinutes(lastIncomplete.start);
        
        if (currentMinutes >= startMinutes) {
          totalMinutes += currentMinutes - startMinutes;
        } else {
          // Current session spans midnight
          totalMinutes += (24 * 60 - startMinutes) + currentMinutes;
        }
      }
    }

    return totalMinutes;
  }

  /**
   * Get current session duration if working
   */
  getCurrentSessionDuration(timeData, currentStatus) {
    if (currentStatus !== 'working') {
      return 0;
    }

    // Find the most recent incomplete session (start time without end time)
    let lastIncomplete = null;
    for (let i = timeData.length - 1; i >= 0; i--) {
      if (timeData[i].start && !timeData[i].end) {
        lastIncomplete = timeData[i];
        break;
      }
    }

    if (!lastIncomplete) {
      // If no incomplete session found but status is working,
      // check if there's any session data at all
      if (timeData.length === 0) {
        return 0;
      }
      // Use the most recent start time
      const mostRecent = timeData[timeData.length - 1];
      if (mostRecent.start) {
        lastIncomplete = { start: mostRecent.start, end: null };
      } else {
        return 0;
      }
    }

    const now = new Date();
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    const startMinutes = this.timeToMinutes(lastIncomplete.start);

    if (currentMinutes >= startMinutes) {
      return currentMinutes - startMinutes;
    } else {
      // Session spans midnight
      return (24 * 60 - startMinutes) + currentMinutes;
    }
  }

  /**
   * Collect all work data
   */
  collectWorkData() {
    const timeData = this.extractTimeData();
    const workStatus = this.getCurrentWorkStatus();
    const totalMinutes = this.calculateDailyTotal(timeData, workStatus);
    const currentSessionMinutes = this.getCurrentSessionDuration(timeData, workStatus);

    return {
      timeData,
      workStatus,
      totalMinutes,
      currentSessionMinutes,
      totalTime: this.minutesToTime(totalMinutes),
      currentSessionTime: this.minutesToTime(currentSessionMinutes),
      timestamp: Date.now()
    };
  }

  /**
   * Send data to extension popup (real-time only)
   */
  async sendDataToExtension(data) {
    try {
      // Only store current session data temporarily
      await chrome.storage.local.set({
        currentWorkData: data,
        lastCalculated: Date.now()
      });

      // Send message to background script for badge update
      chrome.runtime.sendMessage({
        type: 'WORK_DATA_UPDATE',
        data: data
      }).catch(error => {
        // Ignore errors if no listeners
        console.log('Bedalator: No message listeners available');
      });
    } catch (error) {
      console.error('Bedalator: Error sending data to extension:', error);
    }
  }

  /**
   * Start monitoring work time
   */
  startMonitoring() {
    // Initial data collection
    this.updateWorkData();

    // Set up periodic updates (every minute)
    this.updateInterval = setInterval(() => {
      this.updateWorkData();
    }, 60000);

    // Listen for DOM changes that might affect our data
    this.observePageChanges();
  }

  /**
   * Update work data and send to extension
   */
  updateWorkData() {
    const workData = this.collectWorkData();
    
    // Only send update if data has changed significantly
    if (!this.lastKnownData || this.hasSignificantChange(workData, this.lastKnownData)) {
      this.sendDataToExtension(workData);
      this.lastKnownData = workData;
    }
  }

  /**
   * Check if there's a significant change in work data
   */
  hasSignificantChange(newData, oldData) {
    return newData.workStatus !== oldData.workStatus ||
           newData.timeData.length !== oldData.timeData.length ||
           Math.abs(newData.totalMinutes - oldData.totalMinutes) > 0;
  }

  /**
   * Observe page changes for dynamic content updates
   */
  observePageChanges() {
    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach(mutation => {
        // Check if changes affect our target elements
        if (mutation.target.closest('#DataGridVorhandeneBuchungen') ||
            mutation.target.closest('#divTerminalbuttons')) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        // Debounce updates
        clearTimeout(this.updateTimeout);
        this.updateTimeout = setTimeout(() => {
          this.updateWorkData();
        }, 1000);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'textContent']
    });
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
}

// Initialize the time tracker when the page loads
let bedalatorTracker = null;

// Wait for page to be fully loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeTracker);
} else {
  initializeTracker();
}

function initializeTracker() {
  // Check if elements exist before initializing
  const checkAndInit = () => {
    const hasTimeTable = document.querySelector('#DataGridVorhandeneBuchungen') !== null;
    const hasStatusButtons = document.querySelector('#divTerminalbuttons') !== null;

    if (hasTimeTable || hasStatusButtons) {
      bedalatorTracker = new BedalatorTimeTracker();
      console.log('Bedalator: Tracker initialized successfully');
    } else {
      console.log('Bedalator: Required elements not found, tracker not initialized');
    }
  };

  // Try immediately, then with delays for dynamic content
  checkAndInit();
  setTimeout(checkAndInit, 1000);
  setTimeout(checkAndInit, 3000);
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'GET_WORK_DATA') {
    if (bedalatorTracker) {
      const workData = bedalatorTracker.collectWorkData();
      sendResponse(workData);
    } else {
      sendResponse(null);
    }
  } else if (request.type === 'CHECK_PAGE_COMPATIBILITY') {
    // Check if page has required elements
    const hasTimeTable = document.querySelector('#DataGridVorhandeneBuchungen') !== null;
    const hasStatusButtons = document.querySelector('#divTerminalbuttons') !== null;
    sendResponse(hasTimeTable || hasStatusButtons);
  }
  return true; // Keep message channel open for async response
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (bedalatorTracker) {
    bedalatorTracker.stopMonitoring();
  }
});
