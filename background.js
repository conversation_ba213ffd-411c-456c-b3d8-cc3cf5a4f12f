/**
 * Bedalator Background Script (Service Worker)
 * Handles extension lifecycle, data persistence, and communication
 */

class BedalatorBackground {
  constructor() {
    this.trackingTabs = new Map(); // Store tracking tab information
    this.backgroundUpdateInterval = null;
    this.init();
  }

  init() {
    this.setupMessageListeners();
    this.setupStorageListeners();
    this.setupAlarms();
    this.startBackgroundTracking();
    console.log('Bedalator: Background script initialized with cross-tab support');
  }

  /**
   * Set up message listeners for communication between content script and popup
   */
  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.type) {
        case 'WORK_DATA_UPDATE':
          this.handleWorkDataUpdate(request.data, sender.tab);
          break;

        case 'GET_STORED_DATA':
          this.getStoredData().then(data => sendResponse(data));
          return true; // Keep message channel open for async response

        case 'GET_CROSS_TAB_DATA':
          this.getCrossTabData().then(data => sendResponse(data));
          return true;

        case 'REGISTER_TRACKING_TAB':
          this.registerTrackingTab(sender.tab);
          sendResponse({ success: true });
          break;

        case 'CLEAR_DATA':
          this.clearStoredData().then(() => sendResponse({ success: true }));
          return true;

        case 'GET_DAILY_SUMMARY':
          this.getDailySummary().then(summary => sendResponse(summary));
          return true;

        default:
          console.log('Bedalator: Unknown message type:', request.type);
      }
    });
  }

  /**
   * Handle work data updates from content script
   */
  async handleWorkDataUpdate(data, tab) {
    try {
      // Store tab information for cross-tab access
      if (tab) {
        this.trackingTabs.set(tab.id, {
          tabId: tab.id,
          url: tab.url,
          lastUpdate: Date.now(),
          data: data
        });
      }

      // Store only current session data temporarily
      await chrome.storage.local.set({
        currentWorkData: data,
        lastCalculated: Date.now(),
        activeTrackingTab: tab ? tab.id : null
      });

      // Update badge with current work status
      this.updateBadge(data);

      console.log('Bedalator: Work data updated from tab', tab?.id, data);
    } catch (error) {
      console.error('Bedalator: Error handling work data update:', error);
    }
  }

  /**
   * Register a tab as containing tracking data
   */
  registerTrackingTab(tab) {
    if (tab) {
      this.trackingTabs.set(tab.id, {
        tabId: tab.id,
        url: tab.url,
        registered: Date.now(),
        data: null
      });
      console.log('Bedalator: Registered tracking tab', tab.id);
    }
  }

  /**
   * Get work data from any available tracking tab
   */
  async getCrossTabData() {
    try {
      // First try to get stored data
      const stored = await this.getStoredData();
      if (stored) {
        return stored;
      }

      // If no stored data, try to get fresh data from any tracking tab
      for (const [tabId, tabInfo] of this.trackingTabs) {
        try {
          const response = await chrome.tabs.sendMessage(tabId, { type: 'GET_WORK_DATA' });
          if (response) {
            // Update stored data with fresh data
            await chrome.storage.local.set({
              currentWorkData: response,
              lastCalculated: Date.now(),
              activeTrackingTab: tabId
            });
            return response;
          }
        } catch (error) {
          // Tab might be closed or not responding, remove it
          this.trackingTabs.delete(tabId);
          console.log('Bedalator: Removed unresponsive tab', tabId);
        }
      }

      return null;
    } catch (error) {
      console.error('Bedalator: Error getting cross-tab data:', error);
      return null;
    }
  }

  /**
   * Update extension badge based on work status
   */
  updateBadge(data) {
    try {
      const badgeText = data.workStatus === 'working' ? '●' : '';
      const badgeColor = data.workStatus === 'working' ? '#4CAF50' : '#F44336';

      chrome.action.setBadgeText({ text: badgeText });
      chrome.action.setBadgeBackgroundColor({ color: badgeColor });

      // Update title with current work time
      const title = `Bedalator - ${data.totalTime}${data.workStatus === 'working' ? ` (${data.currentSessionTime} aktiv)` : ''}`;
      chrome.action.setTitle({ title });
    } catch (error) {
      console.error('Bedalator: Error updating badge:', error);
    }
  }

  /**
   * Store daily work history
   */
  async storeDailyHistory(data) {
    try {
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const historyKey = `history_${today}`;
      
      const historyEntry = {
        date: today,
        totalMinutes: data.totalMinutes,
        totalTime: data.totalTime,
        sessions: data.timeData,
        lastUpdate: Date.now(),
        workStatus: data.workStatus
      };

      await chrome.storage.local.set({ [historyKey]: historyEntry });

      // Clean up old history (keep last 30 days)
      await this.cleanupOldHistory();
    } catch (error) {
      console.error('Bedalator: Error storing daily history:', error);
    }
  }

  /**
   * Clean up history older than 30 days
   */
  async cleanupOldHistory() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0];

      const allData = await chrome.storage.local.get();
      const keysToRemove = [];

      Object.keys(allData).forEach(key => {
        if (key.startsWith('history_')) {
          const date = key.replace('history_', '');
          if (date < cutoffDate) {
            keysToRemove.push(key);
          }
        }
      });

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`Bedalator: Cleaned up ${keysToRemove.length} old history entries`);
      }
    } catch (error) {
      console.error('Bedalator: Error cleaning up old history:', error);
    }
  }

  /**
   * Start background tracking for continuous updates
   */
  startBackgroundTracking() {
    // Clear any existing interval
    if (this.backgroundUpdateInterval) {
      clearInterval(this.backgroundUpdateInterval);
    }

    // Update every 30 seconds regardless of tab visibility
    let backgroundUpdateCount = 0;
    this.backgroundUpdateInterval = setInterval(async () => {
      backgroundUpdateCount++;
      console.log(`🔄 Bedalator Background: Auto-update #${backgroundUpdateCount} starting...`);
      await this.updateBackgroundData();
      console.log(`✅ Bedalator Background: Auto-update #${backgroundUpdateCount} completed`);
    }, 30000);

    console.log('Bedalator: Background tracking started (30-second intervals)');
  }

  /**
   * Update data from background tabs
   */
  async updateBackgroundData() {
    try {
      // Clean up closed tabs
      await this.cleanupClosedTabs();

      // Try to get fresh data from any tracking tab
      for (const [tabId, tabInfo] of this.trackingTabs) {
        try {
          const response = await chrome.tabs.sendMessage(tabId, { type: 'GET_WORK_DATA' });
          if (response) {
            // Update stored data with fresh data
            await this.handleWorkDataUpdate(response, { id: tabId });
            console.log('Bedalator: Background update from tab', tabId);
            break; // Only need one successful update
          }
        } catch (error) {
          // Tab might not be responding, will be cleaned up later
        }
      }
    } catch (error) {
      console.error('Bedalator: Error in background update:', error);
    }
  }

  /**
   * Clean up tracking tabs that have been closed
   */
  async cleanupClosedTabs() {
    const tabsToRemove = [];

    for (const [tabId, tabInfo] of this.trackingTabs) {
      try {
        await chrome.tabs.get(tabId);
      } catch (error) {
        // Tab doesn't exist anymore
        tabsToRemove.push(tabId);
      }
    }

    tabsToRemove.forEach(tabId => {
      this.trackingTabs.delete(tabId);
      console.log('Bedalator: Cleaned up closed tab', tabId);
    });
  }

  /**
   * Get stored work data
   */
  async getStoredData() {
    try {
      const result = await chrome.storage.local.get(['currentWorkData', 'lastCalculated']);
      return result.currentWorkData || null;
    } catch (error) {
      console.error('Bedalator: Error getting stored data:', error);
      return null;
    }
  }

  /**
   * Clear all stored data
   */
  async clearStoredData() {
    try {
      await chrome.storage.local.clear();
      chrome.action.setBadgeText({ text: '' });
      chrome.action.setTitle({ title: 'Bedalator - Work Time Calculator' });
      console.log('Bedalator: All data cleared');
    } catch (error) {
      console.error('Bedalator: Error clearing data:', error);
    }
  }

  /**
   * Get daily summary for the last 7 days
   */
  async getDailySummary() {
    try {
      const summary = [];
      const today = new Date();

      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateString = date.toISOString().split('T')[0];
        const historyKey = `history_${dateString}`;

        const result = await chrome.storage.local.get(historyKey);
        const dayData = result[historyKey];

        summary.push({
          date: dateString,
          dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
          totalMinutes: dayData?.totalMinutes || 0,
          totalTime: dayData?.totalTime || '00:00',
          sessions: dayData?.sessions || []
        });
      }

      return summary;
    } catch (error) {
      console.error('Bedalator: Error getting daily summary:', error);
      return [];
    }
  }

  /**
   * Set up storage change listeners
   */
  setupStorageListeners() {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local' && changes.bedalatorData) {
        console.log('Bedalator: Storage data changed');
      }
    });
  }

  /**
   * Set up periodic alarms for data cleanup and maintenance
   */
  setupAlarms() {
    // Create alarm for daily cleanup (runs at midnight)
    chrome.alarms.create('dailyCleanup', {
      when: this.getNextMidnight(),
      periodInMinutes: 24 * 60 // 24 hours
    });

    // Listen for alarm events
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === 'dailyCleanup') {
        this.cleanupOldHistory();
      }
    });
  }

  /**
   * Get timestamp for next midnight
   */
  getNextMidnight() {
    const now = new Date();
    const midnight = new Date(now);
    midnight.setHours(24, 0, 0, 0);
    return midnight.getTime();
  }
}

// Initialize background script
const bedalatorBackground = new BedalatorBackground();

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Bedalator: Extension installed');
    
    // Set initial badge
    chrome.action.setBadgeText({ text: '' });
    chrome.action.setTitle({ title: 'Bedalator - Arbeitszeitrechner' });
    
    // Open welcome page or instructions
    chrome.tabs.create({
      url: chrome.runtime.getURL('popup.html')
    });
  } else if (details.reason === 'update') {
    console.log('Bedalator: Extension updated');
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  console.log('Bedalator: Extension started');
});

// Handle tab updates to refresh data when user navigates
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Small delay to allow page to load
    setTimeout(() => {
      chrome.tabs.sendMessage(tabId, { type: 'PAGE_UPDATED' }).catch(() => {
        // Ignore errors if content script is not loaded
      });
    }, 2000);
  }
});

// Handle tab removal to clean up tracking data
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
  if (bedalatorBackground.trackingTabs.has(tabId)) {
    bedalatorBackground.trackingTabs.delete(tabId);
    console.log('Bedalator: Removed tracking tab', tabId);
  }
});

// Handle tab activation to potentially update data
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  const tabId = activeInfo.tabId;
  if (bedalatorBackground.trackingTabs.has(tabId)) {
    // Try to get fresh data from the newly activated tracking tab
    try {
      const response = await chrome.tabs.sendMessage(tabId, { type: 'GET_WORK_DATA' });
      if (response) {
        await bedalatorBackground.handleWorkDataUpdate(response, { id: tabId });
      }
    } catch (error) {
      // Tab might not be ready yet
    }
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup automatically due to default_popup in manifest
  console.log('Bedalator: Extension icon clicked');
});
