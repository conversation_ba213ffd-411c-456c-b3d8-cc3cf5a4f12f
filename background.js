/**
 * Bedalator Background Script (Service Worker)
 * Handles extension lifecycle, data persistence, and communication
 */

class BedalatorBackground {
  constructor() {
    this.init();
  }

  init() {
    this.setupMessageListeners();
    this.setupStorageListeners();
    this.setupAlarms();
    console.log('Bedalator: Background script initialized');
  }

  /**
   * Set up message listeners for communication between content script and popup
   */
  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.type) {
        case 'WORK_DATA_UPDATE':
          this.handleWorkDataUpdate(request.data);
          break;
        
        case 'GET_STORED_DATA':
          this.getStoredData().then(data => sendResponse(data));
          return true; // Keep message channel open for async response
        
        case 'CLEAR_DATA':
          this.clearStoredData().then(() => sendResponse({ success: true }));
          return true;
        
        case 'GET_DAILY_SUMMARY':
          this.getDailySummary().then(summary => sendResponse(summary));
          return true;
        
        default:
          console.log('Bedalator: Unknown message type:', request.type);
      }
    });
  }

  /**
   * Handle work data updates from content script
   */
  async handleWorkDataUpdate(data) {
    try {
      // Store only current session data temporarily
      await chrome.storage.local.set({
        currentWorkData: data,
        lastCalculated: Date.now()
      });

      // Update badge with current work status
      this.updateBadge(data);

      console.log('Bedalator: Work data updated', data);
    } catch (error) {
      console.error('Bedalator: Error handling work data update:', error);
    }
  }

  /**
   * Update extension badge based on work status
   */
  updateBadge(data) {
    try {
      const badgeText = data.workStatus === 'working' ? '●' : '';
      const badgeColor = data.workStatus === 'working' ? '#4CAF50' : '#F44336';

      chrome.action.setBadgeText({ text: badgeText });
      chrome.action.setBadgeBackgroundColor({ color: badgeColor });

      // Update title with current work time
      const title = `Bedalator - ${data.totalTime} total${data.workStatus === 'working' ? ` (${data.currentSessionTime} current)` : ''}`;
      chrome.action.setTitle({ title });
    } catch (error) {
      console.error('Bedalator: Error updating badge:', error);
    }
  }

  /**
   * Store daily work history
   */
  async storeDailyHistory(data) {
    try {
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const historyKey = `history_${today}`;
      
      const historyEntry = {
        date: today,
        totalMinutes: data.totalMinutes,
        totalTime: data.totalTime,
        sessions: data.timeData,
        lastUpdate: Date.now(),
        workStatus: data.workStatus
      };

      await chrome.storage.local.set({ [historyKey]: historyEntry });

      // Clean up old history (keep last 30 days)
      await this.cleanupOldHistory();
    } catch (error) {
      console.error('Bedalator: Error storing daily history:', error);
    }
  }

  /**
   * Clean up history older than 30 days
   */
  async cleanupOldHistory() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0];

      const allData = await chrome.storage.local.get();
      const keysToRemove = [];

      Object.keys(allData).forEach(key => {
        if (key.startsWith('history_')) {
          const date = key.replace('history_', '');
          if (date < cutoffDate) {
            keysToRemove.push(key);
          }
        }
      });

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`Bedalator: Cleaned up ${keysToRemove.length} old history entries`);
      }
    } catch (error) {
      console.error('Bedalator: Error cleaning up old history:', error);
    }
  }

  /**
   * Get stored work data
   */
  async getStoredData() {
    try {
      const result = await chrome.storage.local.get(['bedalatorData', 'lastUpdate']);
      return result.bedalatorData || null;
    } catch (error) {
      console.error('Bedalator: Error getting stored data:', error);
      return null;
    }
  }

  /**
   * Clear all stored data
   */
  async clearStoredData() {
    try {
      await chrome.storage.local.clear();
      chrome.action.setBadgeText({ text: '' });
      chrome.action.setTitle({ title: 'Bedalator - Work Time Calculator' });
      console.log('Bedalator: All data cleared');
    } catch (error) {
      console.error('Bedalator: Error clearing data:', error);
    }
  }

  /**
   * Get daily summary for the last 7 days
   */
  async getDailySummary() {
    try {
      const summary = [];
      const today = new Date();

      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateString = date.toISOString().split('T')[0];
        const historyKey = `history_${dateString}`;

        const result = await chrome.storage.local.get(historyKey);
        const dayData = result[historyKey];

        summary.push({
          date: dateString,
          dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
          totalMinutes: dayData?.totalMinutes || 0,
          totalTime: dayData?.totalTime || '00:00',
          sessions: dayData?.sessions || []
        });
      }

      return summary;
    } catch (error) {
      console.error('Bedalator: Error getting daily summary:', error);
      return [];
    }
  }

  /**
   * Set up storage change listeners
   */
  setupStorageListeners() {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local' && changes.bedalatorData) {
        console.log('Bedalator: Storage data changed');
      }
    });
  }

  /**
   * Set up periodic alarms for data cleanup and maintenance
   */
  setupAlarms() {
    // Create alarm for daily cleanup (runs at midnight)
    chrome.alarms.create('dailyCleanup', {
      when: this.getNextMidnight(),
      periodInMinutes: 24 * 60 // 24 hours
    });

    // Listen for alarm events
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === 'dailyCleanup') {
        this.cleanupOldHistory();
      }
    });
  }

  /**
   * Get timestamp for next midnight
   */
  getNextMidnight() {
    const now = new Date();
    const midnight = new Date(now);
    midnight.setHours(24, 0, 0, 0);
    return midnight.getTime();
  }
}

// Initialize background script
const bedalatorBackground = new BedalatorBackground();

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Bedalator: Extension installed');
    
    // Set initial badge
    chrome.action.setBadgeText({ text: '' });
    chrome.action.setTitle({ title: 'Bedalator - Work Time Calculator' });
    
    // Open welcome page or instructions
    chrome.tabs.create({
      url: chrome.runtime.getURL('popup.html')
    });
  } else if (details.reason === 'update') {
    console.log('Bedalator: Extension updated');
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  console.log('Bedalator: Extension started');
});

// Handle tab updates to refresh data when user navigates
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Small delay to allow page to load
    setTimeout(() => {
      chrome.tabs.sendMessage(tabId, { type: 'PAGE_UPDATED' }).catch(() => {
        // Ignore errors if content script is not loaded
      });
    }, 2000);
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup automatically due to default_popup in manifest
  console.log('Bedalator: Extension icon clicked');
});
