# Bedalator Development Guide

This guide provides detailed information for developers working on the Bedalator Chrome extension.

## 🏗️ Architecture Overview

### Component Interaction Flow

```
Work Tracking Page
       ↓
   Content Script (content.js)
       ↓
   Background Script (background.js)
       ↓
   Chrome Storage API
       ↓
   Popup Interface (popup.js)
```

### Data Flow

1. **Content Script** monitors the work tracking page
2. **HTML Parsing** extracts time data and work status
3. **Data Processing** calculates totals and session durations
4. **Storage** persists data using Chrome Storage API
5. **Background Script** manages lifecycle and badge updates
6. **Popup Interface** displays data and handles user interactions

## 🔧 Development Setup

### Prerequisites

- Google Chrome (latest version)
- Text editor or IDE (VS Code recommended)
- Basic knowledge of JavaScript, HTML, CSS
- Understanding of Chrome Extension APIs

### Local Development

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd bedalator
   ```

2. **Load Extension**
   - Open Chrome: `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select the bedalator directory

3. **Development Workflow**
   - Make code changes
   - Click "Reload" button in Chrome extensions page
   - Test changes on work tracking page
   - Check console for errors

### Debugging

#### Content Script Debugging
```javascript
// Add debug logging in content.js
console.log('Bedalator Debug:', data);

// Inspect in browser DevTools
// F12 → Console → Filter by "Bedalator"
```

#### Background Script Debugging
```javascript
// Access background script console
// chrome://extensions/ → Extension details → "Inspect views: background page"
```

#### Storage Debugging
```javascript
// Check stored data
chrome.storage.local.get(null, (data) => {
  console.log('All stored data:', data);
});
```

## 📁 File Structure Details

### Core Files

#### `manifest.json`
- Extension configuration and permissions
- Defines content scripts and background script
- Specifies popup and icon files

#### `content.js`
- Main logic for HTML parsing and time calculations
- Monitors DOM changes for real-time updates
- Communicates with background script

#### `background.js`
- Service worker for extension lifecycle
- Data persistence and cleanup
- Badge and title updates

#### `popup.html/js/css`
- User interface for displaying work data
- Real-time updates and user interactions
- History view and data management

### Key Classes and Functions

#### BedalatorTimeTracker (content.js)
```javascript
class BedalatorTimeTracker {
  extractTimeData()           // Parse HTML table
  getCurrentWorkStatus()      // Detect work status
  calculateDailyTotal()       // Calculate total time
  collectWorkData()          // Gather all data
  startMonitoring()          // Begin tracking
}
```

#### BedalatorBackground (background.js)
```javascript
class BedalatorBackground {
  handleWorkDataUpdate()     // Process data updates
  updateBadge()             // Update extension badge
  storeDailyHistory()       // Save daily data
  cleanupOldHistory()       // Remove old data
}
```

#### BedalatorPopup (popup.js)
```javascript
class BedalatorPopup {
  loadWorkData()            // Load and display data
  updateUI()                // Update interface
  showHistory()             // Display work history
  clearData()               // Clear all data
}
```

## 🎯 Key Features Implementation

### Time Parsing Algorithm

```javascript
// Extract time data from HTML table
extractTimeData() {
  const rows = document.querySelectorAll('#DataGridVorhandeneBuchungen tbody tr:not(.dx-freespace-row)');
  return Array.from(rows).map(row => {
    const cells = row.querySelectorAll('td');
    return {
      start: cells[0]?.textContent.trim(),
      end: cells[1]?.textContent.trim() || null
    };
  });
}
```

### Work Status Detection

```javascript
// Detect current work status from button text
getCurrentWorkStatus() {
  const button = document.querySelector('#divTerminalbuttons .dx-button-text');
  const buttonText = button?.textContent?.trim();
  
  if (buttonText === 'Gehen') return 'working';
  if (buttonText === 'Kommen') return 'not-working';
  return 'unknown';
}
```

### Time Calculation

```javascript
// Calculate total daily work time
calculateDailyTotal(timeData, currentStatus) {
  let totalMinutes = 0;

  // Add completed sessions
  timeData.forEach(session => {
    if (session.start && session.end) {
      totalMinutes += timeToMinutes(session.end) - timeToMinutes(session.start);
    }
  });

  // Add current session if working
  if (currentStatus === 'working') {
    const lastIncomplete = timeData.find(s => s.start && !s.end);
    if (lastIncomplete) {
      const now = new Date();
      const currentMinutes = now.getHours() * 60 + now.getMinutes();
      totalMinutes += currentMinutes - timeToMinutes(lastIncomplete.start);
    }
  }

  return totalMinutes;
}
```

## 🧪 Testing Guidelines

### Manual Testing Checklist

#### Basic Functionality
- [ ] Extension loads without errors
- [ ] Time data is extracted correctly
- [ ] Work status is detected accurately
- [ ] Real-time updates work properly
- [ ] Data persists across page reloads

#### Edge Cases
- [ ] Sessions spanning midnight
- [ ] Multiple incomplete sessions
- [ ] Invalid time formats
- [ ] Missing HTML elements
- [ ] Network connectivity issues

#### User Interface
- [ ] Popup displays correctly
- [ ] All buttons function properly
- [ ] History view works
- [ ] Error messages display appropriately
- [ ] Loading states work

### Test Data Scenarios

#### Normal Work Day
```javascript
// Test data for typical work sessions
const testSessions = [
  { start: '09:00', end: '12:00' },  // Morning session
  { start: '13:00', end: '17:30' },  // Afternoon session
];
```

#### Active Work Session
```javascript
// Test data with ongoing session
const testSessions = [
  { start: '09:00', end: '12:00' },  // Completed session
  { start: '13:00', end: null },     // Active session
];
```

#### Edge Cases
```javascript
// Test data for edge cases
const edgeCases = [
  { start: '23:30', end: '01:30' },  // Midnight spanning
  { start: '09:00', end: '09:00' },  // Zero duration
  { start: 'invalid', end: '12:00' }, // Invalid format
];
```

## 🔧 Customization Guide

### Adding New Work Tracking Systems

1. **Identify HTML Structure**
   ```javascript
   // Find time data table
   const timeTable = document.querySelector('your-time-table-selector');
   
   // Find status indicator
   const statusElement = document.querySelector('your-status-selector');
   ```

2. **Update Selectors**
   ```javascript
   // In content.js, modify these constants
   const TIME_TABLE_SELECTOR = 'your-time-table-selector';
   const STATUS_BUTTON_SELECTOR = 'your-status-selector';
   ```

3. **Adapt Parsing Logic**
   ```javascript
   // Modify extractTimeData() for different HTML structure
   extractTimeData() {
     // Custom parsing logic for your system
   }
   ```

### Customizing Time Formats

```javascript
// Support different time formats
isValidTimeFormat(timeString) {
  // Add support for 12-hour format
  const time12Regex = /^(1[0-2]|0?[1-9]):[0-5][0-9] (AM|PM)$/i;
  const time24Regex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  
  return time12Regex.test(timeString) || time24Regex.test(timeString);
}
```

### Adding New Features

1. **Content Script Changes**
   - Add new data extraction logic
   - Update data collection function
   - Test with various page states

2. **Background Script Changes**
   - Add new message handlers
   - Update storage schema if needed
   - Add cleanup logic for new data

3. **Popup Interface Changes**
   - Add new UI elements
   - Update display logic
   - Add user interaction handlers

## 🚀 Deployment

### Preparing for Release

1. **Code Review**
   - Check all console.log statements
   - Verify error handling
   - Test edge cases thoroughly

2. **Version Update**
   ```json
   // Update version in manifest.json
   {
     "version": "1.1.0"
   }
   ```

3. **Icon Creation**
   - Create actual PNG icons (16px, 48px, 128px)
   - Replace placeholder references
   - Test icon visibility

4. **Documentation Update**
   - Update README.md
   - Add changelog entry
   - Update version numbers

### Chrome Web Store Submission

1. **Package Extension**
   ```bash
   # Create zip file with all necessary files
   zip -r bedalator-v1.0.0.zip . -x "*.git*" "*.DS_Store*" "node_modules/*"
   ```

2. **Store Listing**
   - Prepare screenshots
   - Write store description
   - Set appropriate categories
   - Define target audience

3. **Review Process**
   - Submit for review
   - Address any feedback
   - Monitor approval status

## 🤝 Contributing

### Code Style

- Use consistent indentation (2 spaces)
- Add JSDoc comments for functions
- Use descriptive variable names
- Handle errors gracefully
- Follow existing patterns

### Pull Request Process

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Update documentation
5. Submit pull request
6. Address review feedback

### Issue Reporting

Include in bug reports:
- Chrome version
- Extension version
- Steps to reproduce
- Expected vs actual behavior
- Console error messages
- Screenshots if applicable

---

This development guide should help you understand and contribute to the Bedalator project effectively.
