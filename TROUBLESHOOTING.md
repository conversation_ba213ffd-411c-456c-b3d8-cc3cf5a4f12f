# Bedalator Troubleshooting Guide

This guide helps resolve common issues when installing and using the Bedalator Chrome extension.

## 🚨 Installation Issues

### Service Worker Registration Failed (Status Code 15)

**Problem**: Extension fails to load with "Service worker registration failed. Status code: 15"

**Solutions**:

1. **Check File Permissions**
   ```bash
   # Ensure all files are readable
   chmod -R 644 *.js *.json *.html *.css
   chmod 755 .
   ```

2. **Verify File Structure**
   ```
   bedalator/
   ├── manifest.json
   ├── background.js
   ├── content.js
   ├── popup.html
   ├── popup.js
   └── popup.css
   ```

3. **Check JavaScript Syntax**
   ```bash
   # Test each JavaScript file for syntax errors
   node -c background.js
   node -c content.js
   node -c popup.js
   ```

4. **Validate manifest.json**
   ```bash
   # Check JSON syntax
   python3 -m json.tool manifest.json
   ```

5. **Chrome Extension Debugging**
   - Go to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Errors" button if visible
   - Check browser console for error messages

### Missing Icon Errors

**Problem**: Extension shows icon-related warnings

**Solutions**:

1. **Temporary Fix**: Remove icon references from manifest.json
2. **Permanent Fix**: Add actual PNG icon files:
   - `icons/icon16.png` (16x16 pixels)
   - `icons/icon48.png` (48x48 pixels)
   - `icons/icon128.png` (128x128 pixels)

### Permission Errors

**Problem**: Extension can't access required APIs

**Solutions**:

1. **Check Required Permissions**:
   ```json
   "permissions": [
     "storage",
     "activeTab",
     "alarms"
   ]
   ```

2. **Host Permissions**:
   ```json
   "host_permissions": [
     "<all_urls>"
   ]
   ```

## 🔧 Runtime Issues

### Extension Not Detecting Work Data

**Problem**: Popup shows "No work data available"

**Debugging Steps**:

1. **Check Page Compatibility**
   - Open browser DevTools (F12)
   - Go to Console tab
   - Look for "Bedalator" messages
   - Verify target elements exist:
     ```javascript
     // Run in console
     document.querySelector('#DataGridVorhandeneBuchungen')
     document.querySelector('#divTerminalbuttons')
     ```

2. **Content Script Issues**
   - Check if content script is injected:
     ```javascript
     // In console, look for:
     console.log('Bedalator: Time tracking initialized')
     ```

3. **Manual Data Refresh**
   - Click the "Refresh" button in popup
   - Check if data appears after refresh

### Incorrect Time Calculations

**Problem**: Time totals don't match expected values

**Debugging Steps**:

1. **Check Time Format**
   - Ensure times are in HH:MM format
   - Verify no extra spaces or characters

2. **Inspect Session Data**
   - Open popup and check session list
   - Verify start/end times are correct

3. **Console Debugging**
   ```javascript
   // Check extracted data in console
   console.log('Time data:', timeData);
   console.log('Work status:', workStatus);
   ```

### Status Detection Not Working

**Problem**: Extension shows "Unknown" status

**Solutions**:

1. **Check Button Text**
   - Verify button contains "Gehen" or "Kommen"
   - Check for extra whitespace or formatting

2. **Inspect Button Element**
   ```javascript
   // In console
   const button = document.querySelector('#divTerminalbuttons .dx-button-text');
   console.log('Button text:', button?.textContent);
   ```

## 🔍 Advanced Debugging

### Background Script Debugging

1. **Access Background Console**
   - Go to `chrome://extensions/`
   - Find Bedalator extension
   - Click "Inspect views: background page"

2. **Check Storage Data**
   ```javascript
   // In background console
   chrome.storage.local.get(null, (data) => {
     console.log('All stored data:', data);
   });
   ```

### Content Script Debugging

1. **Check Script Injection**
   - Open page with work tracking system
   - Open DevTools (F12)
   - Look for Bedalator console messages

2. **Manual Function Testing**
   ```javascript
   // Test time parsing
   const tracker = new BedalatorTimeTracker();
   console.log(tracker.extractTimeData());
   console.log(tracker.getCurrentWorkStatus());
   ```

### Network and Performance

1. **Check for Conflicts**
   - Disable other extensions temporarily
   - Test in incognito mode
   - Clear browser cache

2. **Performance Issues**
   - Monitor CPU usage in Task Manager
   - Check for memory leaks
   - Reduce update frequency if needed

## 🛠️ Common Fixes

### Reset Extension Data

```javascript
// Clear all stored data
chrome.storage.local.clear(() => {
  console.log('All data cleared');
});
```

### Reload Extension

1. Go to `chrome://extensions/`
2. Find Bedalator
3. Click reload button (🔄)
4. Test functionality

### Reinstall Extension

1. Remove extension from Chrome
2. Delete and re-download files
3. Load unpacked extension again
4. Test on work tracking page

## 📞 Getting Help

If issues persist:

1. **Check Console Errors**
   - Browser console (F12)
   - Extension background page console
   - Note exact error messages

2. **Gather Information**
   - Chrome version
   - Operating system
   - Extension version
   - Work tracking system URL (if shareable)

3. **Create Issue Report**
   - Include error messages
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if helpful

## 🔧 Development Mode Tips

### Enable Verbose Logging

Add to content.js for more detailed logs:
```javascript
const DEBUG_MODE = true;
if (DEBUG_MODE) {
  console.log('Bedalator Debug:', data);
}
```

### Test with Sample Data

Create test data for development:
```javascript
const testData = {
  timeData: [
    { start: '09:00', end: '12:00' },
    { start: '13:00', end: null }
  ],
  workStatus: 'working'
};
```

### Monitor Storage Changes

```javascript
chrome.storage.onChanged.addListener((changes, namespace) => {
  console.log('Storage changed:', changes);
});
```

---

This troubleshooting guide should help resolve most common issues with the Bedalator extension.
